"""
Enhanced Logger with Conversation ID Support

统一的日志记录器，自动在日志消息中包含conversation_id信息。
支持向后兼容，不影响现有代码的使用方式。

核心功能：
1. 自动在日志中包含conversation_id
2. 格式：[时间] - 级别 - 名称 - [conversation_id] 消息
3. 向后兼容现有logger使用方式
4. 离线任务保护机制
"""

import logging
import sys

BASE_FORMAT = "[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s"
CONVERSATION_FORMAT = "[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] [%(conversation_id)s] %(message)s"

class ConversationFormatter(logging.Formatter):
    """
    高性能的conversation_id日志格式化器

    支持配置化的格式定义，避免字符串分割操作，提升性能。
    自动在日志消息中包含conversation_id，如果当前线程没有则使用原格式。
    """

    def __init__(self,
                 base_format: str = BASE_FORMAT,
                 conversation_format: str = CONVERSATION_FORMAT,
                 datefmt: str = None):
        """
        初始化格式化器

        Args:
            base_format: 基础日志格式（无conversation_id时使用）
            conversation_format: 包含conversation_id的日志格式
            datefmt: 日期格式字符串
        """
        # 使用基础格式初始化父类
        super().__init__(base_format, datefmt)
        self.base_format = base_format
        self.conversation_format = conversation_format
        self._base_formatter = logging.Formatter(base_format, datefmt)
        self._conversation_formatter = logging.Formatter(conversation_format, datefmt)

    def format(self, record: logging.LogRecord) -> str:
        """
        高性能格式化日志记录

        根据是否有conversation_id选择不同的格式化器，
        避免字符串分割和重组操作。
        """
        # 延迟导入以避免循环导入
        try:
            from src.utils.conversation_context import safe_get_conversation_id
            conversation_id = safe_get_conversation_id()
        except ImportError:
            conversation_id = None

        if conversation_id:
            # 临时添加conversation_id到record中
            original_conversation_id = getattr(record, 'conversation_id', None)
            record.conversation_id = conversation_id
            try:
                # 使用包含conversation_id的格式化器
                return self._conversation_formatter.format(record)
            finally:
                # 恢复原始状态
                if original_conversation_id is None:
                    delattr(record, 'conversation_id')
                else:
                    record.conversation_id = original_conversation_id
        else:
            # 使用基础格式化器
            return self._base_formatter.format(record)

# 配置根logger以防止重复日志
root_logger = logging.getLogger()
if not root_logger.handlers:
    root_logger.addHandler(logging.NullHandler())

# 创建统一的logger实例
logger = logging.getLogger("chat_bi")

# 只在未配置时进行配置
if not logger.handlers:
    logger.setLevel(logging.INFO)
    logger.propagate = False

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 使用高性能的conversation_id格式化器
    enhanced_formatter = ConversationFormatter(
        base_format=BASE_FORMAT,
        conversation_format=CONVERSATION_FORMAT,
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(enhanced_formatter)

    # 添加处理器到logger
    logger.addHandler(console_handler)


def get_logger(name: str = "chat_bi") -> logging.Logger:
    """
    获取logger实例
    
    Args:
        name: logger名称
        
    Returns:
        配置好的logger实例
    """
    if name == "chat_bi":
        return logger
    else:
        # 为其他名称创建新的logger
        new_logger = logging.getLogger(name)
        if not new_logger.handlers:
            new_logger.setLevel(logging.INFO)
            new_logger.propagate = False
            
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            enhanced_formatter = ConversationFormatter(
                base_format=BASE_FORMAT,
                conversation_format=CONVERSATION_FORMAT,
                datefmt="%Y-%m-%d %H:%M:%S"
            )
            console_handler.setFormatter(enhanced_formatter)
            new_logger.addHandler(console_handler)
        
        return new_logger


# 向后兼容：提供直接调用的方法
def info(msg, *args, **kwargs):
    """向后兼容的info方法"""
    logger.info(msg, *args, **kwargs)


def debug(msg, *args, **kwargs):
    """向后兼容的debug方法"""
    logger.debug(msg, *args, **kwargs)


def warning(msg, *args, **kwargs):
    """向后兼容的warning方法"""
    logger.warning(msg, *args, **kwargs)


def error(msg, *args, **kwargs):
    """向后兼容的error方法"""
    logger.error(msg, *args, **kwargs)


def exception(msg, *args, **kwargs):
    """向后兼容的exception方法"""
    logger.exception(msg, *args, **kwargs)


def critical(msg, *args, **kwargs):
    """向后兼容的critical方法"""
    logger.critical(msg, *args, **kwargs)